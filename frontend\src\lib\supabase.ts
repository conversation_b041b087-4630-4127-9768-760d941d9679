import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/database';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env file.');
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// Handle auth errors globally
supabase.auth.onAuthStateChange((event, session) => {
  if (event === 'TOKEN_REFRESHED') {
    console.log('Token refreshed successfully');
  } else if (event === 'SIGNED_OUT') {
    console.log('User signed out');
    // Clear any cached user data
    localStorage.removeItem('supabase.auth.token');
  }
});

// Add error handling for refresh token failures
const originalRefreshSession = supabase.auth.refreshSession;
supabase.auth.refreshSession = async function(refreshToken?: { refresh_token: string }) {
  try {
    return await originalRefreshSession.call(this, refreshToken);
  } catch (error: any) {
    console.error('Refresh token error:', error);

    // If refresh token is invalid, sign out the user
    if (error?.message?.includes('Invalid Refresh Token') ||
        error?.message?.includes('Refresh Token Not Found')) {
      console.log('Invalid refresh token detected, signing out user');
      await supabase.auth.signOut();
      // Clear all auth data
      clearAuthData();
      // Reload the page to reset the app state
      window.location.reload();
    }

    throw error;
  }
};

// Helper function to handle Supabase errors
export const handleSupabaseError = (error: any) => {
  console.error('Supabase error:', error);
  
  if (error?.message) {
    return error.message;
  }
  
  return 'An unexpected error occurred. Please try again.';
};

// Helper function to check if user is authenticated
export const isAuthenticated = async () => {
  const { data: { session } } = await supabase.auth.getSession();
  return !!session;
};

// Helper function to get current user
export const getCurrentUser = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  return user;
};

// Helper function to clear all auth data
export const clearAuthData = () => {
  // Clear Supabase auth token
  localStorage.removeItem('supabase.auth.token');

  // Clear any other auth-related items
  const keysToRemove = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith('supabase.auth')) {
      keysToRemove.push(key);
    }
  }

  keysToRemove.forEach(key => localStorage.removeItem(key));
};

// Helper function to sign out
export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  } finally {
    // Always clear auth data, even if signOut fails
    clearAuthData();
  }
};

// Make clearAuthData available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).clearSupabaseAuth = () => {
    clearAuthData();
    console.log('Supabase auth data cleared. Reloading page...');
    window.location.reload();
  };
}
