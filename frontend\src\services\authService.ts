/**
 * Authentication Service for Sharyoo Frontend
 * Integrates with Supabase Auth and Backend API
 */

import { supabase, clearAuthData } from '../lib/supabase';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

export interface Tenant {
  id: string;
  store_name: string;
  store_slug: string;
  subdomain: string;
  status: string;
  subscription_plan: string;
  max_products: number;
  max_orders_per_month: number;
  max_storage_mb: number;
  store_description?: string;
  store_logo_url?: string;
  currency: string;
  language: string;
  business_type?: string;
  contact_email?: string;
  contact_phone?: string;
}

export interface User {
  id: string;
  auth_id: string;
  tenant_id: string;
  email: string;
  first_name: string;
  last_name: string;
  gender?: string;
  role: string;
  permissions: string[];
  is_active: boolean;
  email_verified: boolean;
  profile_picture_url?: string;
  phone?: string;
  created_at: string;
  updated_at: string;
  tenant?: Tenant;
}

export interface AuthResponse {
  success: boolean;
  message?: string;
  data?: {
    user: User;
    session?: any;
    access_token?: string;
  };
  error?: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  gender: 'male' | 'female';
}

export interface LoginData {
  email: string;
  password: string;
}

class AuthService {
  private currentUser: User | null = null;
  private accessToken: string | null = null;

  /**
   * Register a new user
   */
  async register(userData: RegisterData): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_URL}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const result = await response.json();

      if (result.success && result.data?.session) {
        this.setSession(result.data.session);
        this.currentUser = result.data.user;
      }

      return result;
    } catch (error) {
      console.error('Registration error:', error);
      return {
        success: false,
        error: 'Network error. Please try again.',
      };
    }
  }

  /**
   * Login user
   */
  async login(credentials: LoginData): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      const result = await response.json();

      if (result.success && result.data?.session) {
        this.setSession(result.data.session);
        this.currentUser = result.data.user;
        this.accessToken = result.data.access_token;
      }

      return result;
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        error: 'Network error. Please try again.',
      };
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      // Call backend logout endpoint
      if (this.accessToken) {
        await fetch(`${API_URL}/api/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
          },
        });
      }

      // Sign out from Supabase
      await supabase.auth.signOut();

      // Clear local state
      this.currentUser = null;
      this.accessToken = null;

      // Clear all auth data
      clearAuthData();

    } catch (error) {
      console.error('Logout error:', error);
      // Clear local state even if logout fails
      this.currentUser = null;
      this.accessToken = null;

      // Clear all auth data even if logout fails
      clearAuthData();

      // Force sign out from Supabase
      try {
        await supabase.auth.signOut();
      } catch (signOutError) {
        console.error('Force sign out error:', signOutError);
      }
    }
  }

  /**
   * Get current user
   */
  async getCurrentUser(): Promise<User | null> {
    if (this.currentUser) {
      return this.currentUser;
    }

    try {
      // Get session from Supabase
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError) {
        console.error('Session error:', sessionError);

        // If it's a refresh token error, clear the session
        if (sessionError.message?.includes('Invalid Refresh Token') ||
            sessionError.message?.includes('Refresh Token Not Found')) {
          await this.logout();
        }
        return null;
      }

      if (!session) {
        return null;
      }

      // Verify token with backend
      const response = await fetch(`${API_URL}/api/auth/verify-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: session.access_token }),
      });

      const result = await response.json();

      if (result.success && result.data?.user) {
        this.currentUser = result.data.user;
        this.accessToken = session.access_token;
        return this.currentUser;
      }

      return null;
    } catch (error: any) {
      console.error('Get current user error:', error);

      // If it's a refresh token error, clear the session
      if (error?.message?.includes('Invalid Refresh Token') ||
          error?.message?.includes('Refresh Token Not Found')) {
        await this.logout();
      }

      return null;
    }
  }

  /**
   * Check if user is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    const user = await this.getCurrentUser();
    return !!user;
  }

  /**
   * Get access token for API calls
   */
  async getAccessToken(): Promise<string | null> {
    if (this.accessToken) {
      return this.accessToken;
    }

    const { data: { session } } = await supabase.auth.getSession();
    if (session?.access_token) {
      this.accessToken = session.access_token;
      return this.accessToken;
    }

    return null;
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<boolean> {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.refresh_token) {
        return false;
      }

      const response = await fetch(`${API_URL}/api/auth/refresh-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh_token: session.refresh_token }),
      });

      const result = await response.json();

      if (result.success && result.data?.session) {
        this.setSession(result.data.session);
        this.accessToken = result.data.access_token;
        return true;
      }

      return false;
    } catch (error) {
      console.error('Token refresh error:', error);
      return false;
    }
  }

  /**
   * Send password reset email
   */
  async forgotPassword(email: string): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_URL}/api/auth/forgot-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      return await response.json();
    } catch (error) {
      console.error('Forgot password error:', error);
      return {
        success: false,
        error: 'Network error. Please try again.',
      };
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(accessToken: string, refreshToken: string, newPassword: string): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_URL}/api/auth/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          access_token: accessToken,
          refresh_token: refreshToken,
          new_password: newPassword,
        }),
      });

      return await response.json();
    } catch (error) {
      console.error('Reset password error:', error);
      return {
        success: false,
        error: 'Network error. Please try again.',
      };
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(updates: Partial<User>): Promise<AuthResponse> {
    try {
      const token = await this.getAccessToken();
      
      if (!token) {
        return {
          success: false,
          error: 'Not authenticated',
        };
      }

      const response = await fetch(`${API_URL}/api/auth/profile`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      const result = await response.json();

      if (result.success && result.data?.user) {
        this.currentUser = result.data.user;
      }

      return result;
    } catch (error) {
      console.error('Update profile error:', error);
      return {
        success: false,
        error: 'Network error. Please try again.',
      };
    }
  }

  /**
   * Set Supabase session
   */
  private setSession(session: any): void {
    supabase.auth.setSession(session);
  }

  /**
   * Listen to auth state changes
   */
  onAuthStateChange(callback: (user: User | null) => void): () => void {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session) {
          await this.getCurrentUser();
          callback(this.currentUser);
        } else if (event === 'SIGNED_OUT') {
          this.currentUser = null;
          this.accessToken = null;
          callback(null);
        }
      }
    );

    return () => subscription.unsubscribe();
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
