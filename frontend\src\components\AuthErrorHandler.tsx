import React, { useEffect, useState } from 'react';
import { clearAuthData } from '../lib/supabase';

interface AuthErrorHandlerProps {
  children: React.ReactNode;
}

const AuthErrorHandler: React.FC<AuthErrorHandlerProps> = ({ children }) => {
  const [showClearButton, setShowClearButton] = useState(false);

  useEffect(() => {
    // Listen for auth errors in the console
    const originalConsoleError = console.error;
    console.error = (...args) => {
      const message = args.join(' ');
      if (message.includes('Invalid Refresh Token') || 
          message.includes('Refresh Token Not Found') ||
          message.includes('AuthApiError')) {
        setShowClearButton(true);
      }
      originalConsoleError.apply(console, args);
    };

    return () => {
      console.error = originalConsoleError;
    };
  }, []);

  const handleClearSession = () => {
    clearAuthData();
    window.location.reload();
  };

  return (
    <>
      {children}
      {showClearButton && (
        <div className="fixed bottom-4 right-4 z-50">
          <div className="bg-red-500 text-white p-4 rounded-lg shadow-lg max-w-sm">
            <p className="text-sm mb-2">
              Authentication error detected. Clear session data?
            </p>
            <div className="flex gap-2">
              <button
                onClick={handleClearSession}
                className="bg-white text-red-500 px-3 py-1 rounded text-sm font-medium hover:bg-gray-100"
              >
                Clear Session
              </button>
              <button
                onClick={() => setShowClearButton(false)}
                className="bg-red-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-red-700"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AuthErrorHandler;
