import React, { useState, useEffect } from 'react';
import { Eye, Package, Truck, CheckCircle, Clock, XCircle, ShoppingBag } from 'lucide-react';

interface Order {
  id: string;
  customer: string;
  email: string;
  date: string;
  time: string;
  total: string;
  status: string;
  items: number;
  paymentMethod: string;
  shippingAddress: string;
}

const RecentOrders: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call to fetch orders
    const fetchOrders = async () => {
      try {
        // In a real app, this would be an API call
        // For now, we'll simulate an empty response for new users
        const response = await new Promise<Order[]>((resolve) => {
          setTimeout(() => {
            // Return empty array for new users
            resolve([]);
          }, 1000);
        });

        setOrders(response);
      } catch (error) {
        console.error('Error fetching orders:', error);
        setOrders([]);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, []);

  // Sample order for demonstration (this would be removed in production)
  const sampleOrders = [
    {
      id: '#ORD-2024-015',
      customer: 'TAHAR NAIT ALI',
      email: '<EMAIL>',
      date: '15/01/2024',
      time: '14:30',
      total: '4,250.00 DA',
      status: 'Livré',
      items: 3,
      paymentMethod: 'Carte bancaire',
      shippingAddress: 'Alger Centre, Alger'
    },
    {
      id: '#ORD-2024-014',
      customer: 'Fatima Zahra Benali',
      email: '<EMAIL>',
      date: '15/01/2024',
      time: '12:15',
      total: '1,850.00 DA',
      status: 'En cours',
      items: 2,
      paymentMethod: 'Paiement à la livraison',
      shippingAddress: 'Oran, Oran'
    },
    {
      id: '#ORD-2024-013',
      customer: 'Karim Hadj Mohamed',
      email: '<EMAIL>',
      date: '14/01/2024',
      time: '16:45',
      total: '3,600.00 DA',
      status: 'Expédié',
      items: 5,
      paymentMethod: 'Virement bancaire',
      shippingAddress: 'Constantine, Constantine'
    },
    {
      id: '#ORD-2024-012',
      customer: 'Amina Boucherit',
      email: '<EMAIL>',
      date: '14/01/2024',
      time: '10:20',
      total: '2,750.00 DA',
      status: 'Livré',
      items: 4,
      paymentMethod: 'Carte bancaire',
      shippingAddress: 'Annaba, Annaba'
    },
    {
      id: '#ORD-2024-011',
      customer: 'Youcef Messaoudi',
      email: '<EMAIL>',
      date: '13/01/2024',
      time: '09:30',
      total: '890.00 DA',
      status: 'En attente',
      items: 1,
      paymentMethod: 'Paiement à la livraison',
      shippingAddress: 'Sétif, Sétif'
    },
    {
      id: '#ORD-2024-010',
      customer: 'Leila Cherif',
      email: '<EMAIL>',
      date: '13/01/2024',
      time: '15:10',
      total: '5,200.00 DA',
      status: 'Annulé',
      items: 6,
      paymentMethod: 'Carte bancaire',
      shippingAddress: 'Tlemcen, Tlemcen'
    },
    {
      id: '#ORD-2024-009',
      customer: 'Rachid Benmohamed',
      email: '<EMAIL>',
      date: '12/01/2024',
      time: '11:45',
      total: '3,400.00 DA',
      status: 'Traitement',
      items: 3,
      paymentMethod: 'Virement bancaire',
      shippingAddress: 'Béjaïa, Béjaïa'
    },
    {
      id: '#ORD-2024-008',
      customer: 'Nadia Hamidi',
      email: '<EMAIL>',
      date: '12/01/2024',
      time: '08:20',
      total: '2,800.00 DA',
      status: 'Expédié',
      items: 4,
      paymentMethod: 'Paiement à la livraison',
      shippingAddress: 'Batna, Batna'
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Livré': return <CheckCircle className="w-4 h-4" />;
      case 'Expédié': return <Truck className="w-4 h-4" />;
      case 'En cours': case 'Traitement': return <Package className="w-4 h-4" />;
      case 'En attente': return <Clock className="w-4 h-4" />;
      case 'Annulé': return <XCircle className="w-4 h-4" />;
      default: return <Package className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Livré': return 'bg-green-100 text-green-800';
      case 'Expédié': return 'bg-blue-100 text-blue-800';
      case 'En cours': case 'Traitement': return 'bg-yellow-100 text-yellow-800';
      case 'En attente': return 'bg-gray-100 text-gray-800';
      case 'Annulé': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-6 bg-white rounded-xl shadow-sm border border-gray-200">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-bold text-gray-900">Commandes récentes</h2>
          <p className="text-sm text-gray-600 mt-1">Dernières commandes passées sur votre boutique</p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
            {orders.length} commandes
          </div>
          <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
            Voir tout
          </button>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Chargement des commandes...</span>
        </div>
      ) : orders.length === 0 ? (
        <div className="text-center py-12">
          <ShoppingBag className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune commande</h3>
          <p className="text-gray-500 mb-6">Vous n'avez pas encore reçu de commandes. Commencez par ajouter des produits à votre boutique.</p>
          <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
            Ajouter un produit
          </button>
        </div>
      ) : (
        <>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Commande</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Client</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date & Heure</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Articles</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Total</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Statut</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {orders.slice(0, 5).map((order, index) => (
                <tr key={index} className="hover:bg-gray-50 transition-colors duration-150">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-semibold text-blue-600">{order.id}</div>
                      <div className="text-xs text-gray-500">{order.paymentMethod}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8">
                        <div className="h-8 w-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-xs">
                          {order.customer.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </div>
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">{order.customer}</div>
                        <div className="text-xs text-gray-500">{order.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{order.date}</div>
                      <div className="text-xs text-gray-500">{order.time}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Package className="w-4 h-4 text-gray-400 mr-2" />
                      <span className="text-sm font-medium text-gray-900">{order.items}</span>
                      <span className="text-xs text-gray-500 ml-1">article{order.items > 1 ? 's' : ''}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-semibold text-gray-900">{order.total}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>
                      {getStatusIcon(order.status)}
                      <span className="ml-1.5">{order.status}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button className="flex items-center px-3 py-1.5 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors duration-150">
                      <Eye className="w-4 h-4 mr-1.5" />
                      <span className="text-sm font-medium">Voir</span>
                    </button>
                  </td>
                </tr>
                ))}
              </tbody>
            </table>
          </div>

          {orders.length > 5 && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <button className="w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium py-2 hover:bg-blue-50 rounded-lg transition-colors duration-150">
                Voir toutes les commandes ({orders.length})
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default RecentOrders;
