import React, { useState, useEffect, useRef } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import Navbar from './Navbar';
import Dashboard from './Dashboard';
import Hero from './Hero';
import Features from './Features';
import HowItWorks from './HowItWorks';
import Pricing from './Pricing';
import Testimonials from './Testimonials';
import CallToAction from './CallToAction';
import Footer from './Footer';
import AuthModal from './AuthModal';
import ContentModal from './ContentModal';
import NotificationToast from './NotificationToast';
import FaqModal from './FaqModal';
import ContactModal from './ContactModal';
import DocumentationModal from './DocumentationModal';
import { useLanguage } from '../context/LanguageContext';
import { useAuth } from '../context/AuthContext';
import UserProfile from './UserProfile';
import EmptyState, { WelcomeNewTenant, EmptyTenantStats } from './EmptyState';
import { dashboardService, DashboardData } from '../services/dashboardService';

import { ArrowUpRight, DollarSign, ShoppingBag, Users, ArrowDownRight, Eye, Clock, Package, AlertCircle, XCircle, Star, TrendingUp, BarChart2, Calendar, ChevronDown } from 'lucide-react';

// Import new page components
import RecentOrders from './pages/orders/RecentOrders';
import PopularProducts from './pages/orders/PopularProducts';
import Activities from './pages/orders/Activities';
import AllProducts from './pages/products/AllProducts';
import AddProduct from './pages/products/AddProduct';
import Categories from './pages/products/Categories';
import ClientList from './pages/clients/ClientList';
import ClientGroups from './pages/clients/ClientGroups';
import SalesReports from './pages/analytics/SalesReports';
import ProductPerformance from './pages/analytics/ProductPerformance';
import GeneralSettings from './pages/settings/GeneralSettings';
import ShippingSettings from './pages/settings/ShippingSettings';
import PaymentSettings from './pages/settings/PaymentSettings';
import OrdersSummary from './pages/orders/OrdersSummary';
import ProductsSummary from './pages/products/ProductsSummary';
import ClientsSummary from './pages/clients/ClientsSummary';
import AnalyticsSummary from './pages/analytics/AnalyticsSummary';
import SettingsSummary from './pages/settings/SettingsSummary'; // Import SettingsSummary


// Import new page wrappers
import OrdersPage from './pages/orders/OrdersPage';
import ProductsPage from './pages/products/ProductsPage';
import ClientsPage from './pages/clients/ClientsPage';
import AnalyticsPage from './pages/analytics/AnalyticsPage';
import SettingsPage from './pages/settings/SettingsPage';
import BoutiquePage from './pages/boutique/BoutiquePage';

const AppContent: React.FC = () => {
  const { t } = useLanguage();
  const { user, isAuthenticated, logout } = useAuth();

  const [activeContent, setActiveContent] = useState('overview'); // State to manage active content for sidebar navigation
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loadingDashboard, setLoadingDashboard] = useState(true);

  const handleLogin = () => {
    closeAllModals();
  };

  const handleLogout = async () => {
    await logout();
  };

  // Load dashboard data when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      loadDashboardData();
    }
  }, [isAuthenticated, user]);

  const loadDashboardData = async () => {
    setLoadingDashboard(true);
    try {
      const data = await dashboardService.getDashboardData();
      setDashboardData(data);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // Set null as fallback - the component will handle empty state
      setDashboardData(null);
    } finally {
      setLoadingDashboard(false);
    }
  };

  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showRegisterModal, setShowRegisterModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [showFaqModal, setShowFaqModal] = useState(false);
  const [showContactModal, setShowContactModal] = useState(false);
  const [showDocumentationModal, setShowDocumentationModal] = useState(false);
  const [showAboutUsModal, setShowAboutUsModal] = useState(false);
  const [showCareersModal, setShowCareersModal] = useState(false);
  const [showBlogModal, setShowBlogModal] = useState(false);
  const [showEasyStoreCreationModal, setShowEasyStoreCreationModal] = useState(false);
  const [showArabicSupportModal, setShowArabicSupportModal] = useState(false);
  const [showDataSecurityModal, setShowDataSecurityModal] = useState(false);
  const [returnToAuthType, setReturnToAuthType] = useState<'login' | 'register' | null>(null);
  const [notifications, setNotifications] = useState<{ id: number; message: string; type: 'success' | 'error' | 'info' }[]>([]);

  const showNotification = (message: string, type: 'success' | 'error' | 'info') => {
    const id = Date.now();
    setNotifications((prev) => [...prev, { id, message, type }]);
  };

  const removeNotification = (id: number) => {
    setNotifications((prev) => prev.filter((n) => n.id !== id));
  };

  const closeAllModals = () => {
    setShowLoginModal(false);
    setShowRegisterModal(false);
    setShowPrivacyModal(false);
    setShowTermsModal(false);
    setShowFaqModal(false);
    setShowContactModal(false);
    setShowDocumentationModal(false);
    setShowAboutUsModal(false);
    setShowCareersModal(false);
    setShowBlogModal(false);
    setShowEasyStoreCreationModal(false);
    setShowArabicSupportModal(false);
    setShowDataSecurityModal(false);
    setReturnToAuthType(null);
  };

  const openLoginModal = () => {
    closeAllModals();
    setShowLoginModal(true);
  };

  const openRegisterModal = () => {
    closeAllModals();
    setShowRegisterModal(true);
  };

  const closeAuthModal = () => {
    setShowLoginModal(false);
    setShowRegisterModal(false);
  };

  const openPrivacyModal = (fromAuth: boolean = false) => {
    closeAllModals();
    setShowPrivacyModal(true);
    if (fromAuth) setReturnToAuthType('register');
  };

  const closePrivacyModal = () => {
    setShowPrivacyModal(false);
    if (returnToAuthType === 'register') {
      setShowRegisterModal(true);
    } else if (returnToAuthType === 'login') {
      setShowLoginModal(true);
    }
    setReturnToAuthType(null);
  };

  const openTermsModal = (fromAuth: boolean = false) => {
    closeAllModals();
    setShowTermsModal(true);
    if (fromAuth) setReturnToAuthType('register');
  };

  const closeTermsModal = () => {
    setShowTermsModal(false);
    if (returnToAuthType === 'register') {
      setShowRegisterModal(true);
    } else if (returnToAuthType === 'login') {
      setShowLoginModal(true);
    }
    setReturnToAuthType(null);
  };

  const openFaqModal = () => {
    closeAllModals();
    setShowFaqModal(true);
  };

  const closeFaqModal = () => {
    setShowFaqModal(false);
  };

  const openContactModal = () => {
    closeAllModals();
    setShowContactModal(true);
  };

  const closeContactModal = () => {
    setShowContactModal(false);
  };

  const openDocumentationModal = () => {
    closeAllModals();
    setShowDocumentationModal(true);
  };

  const closeDocumentationModal = () => {
    setShowDocumentationModal(false);
  };

  const openAboutUsModal = () => {
    closeAllModals();
    setShowAboutUsModal(true);
  };

  const closeAboutUsModal = () => {
    setShowAboutUsModal(false);
  };

  const openCareersModal = () => {
    closeAllModals();
    setShowCareersModal(true);
  };

  const closeCareersModal = () => {
    setShowCareersModal(false);
  };

  const openBlogModal = () => {
    closeAllModals();
    setShowBlogModal(true);
  };

  const closeBlogModal = () => {
    setShowBlogModal(false);
  };

  const openEasyStoreCreationModal = () => {
    closeAllModals();
    setShowEasyStoreCreationModal(true);
  };

  const closeEasyStoreCreationModal = () => {
    setShowEasyStoreCreationModal(false);
  };

  const openArabicSupportModal = () => {
    closeAllModals();
    setShowArabicSupportModal(true);
  };

  const closeArabicSupportModal = () => {
    setShowArabicSupportModal(false);
  };

  const openDataSecurityModal = () => {
    closeAllModals();
    setShowDataSecurityModal(true);
  };

  const closeDataSecurityModal = () => {
    setShowDataSecurityModal(false);
  };

  const privacyPolicyContent = `
Politique de Confidentialité de Sharyoo 
Dernière mise à jour : 25 MAI 2025 
Applicable à tous les utilisateurs de la plateforme Sharyoo (site web et application 
mobile). 
 
1. Introduction 
Chez Sharyoo, nous nous engageons à protéger vos données personnelles et votre 
vie privée. Cette politique explique comment nous collectons, utilisons, partageons et 
sécurisons vos informations lorsque vous utilisez notre plateforme e-commerce. En 
utilisant Sharyoo, vous acceptez les termes de cette politique. 
 
2. Données Collectées 
Nous collectons les données suivantes : 
 Informations personnelles : Nom, prénom, adresse, e-mail, numéro de téléphone, 
date de naissance. 
 Données de paiement : Informations de carte bancaire (traitées de manière 
sécurisée via nos partenaires de paiement agrées). 
 Données techniques : Adresse IP, type de navigateur, historique de navigation, 
données de localisation. 
 Données transactionnelles : Historique des commandes, préférences d'achat, 
retours. 
 Contenus générés : Avis clients, commentaires, messages via le service client. 
 
3. Utilisation des Données 
Vos données sont utilisées pour : 
 Traiter vos commandes et livraisons. 
 Gérer votre compte utilisateur. 
 Vous fournir un service client personnalisé. 
 Envoyer des newsletters et offres promotionnelles (avec votre consentement). 
 Améliorer nos services via l’analyse statistique. 
 Respecter nos obligations légales (ex : facturation électronique conforme à la loi 
algérienne). 
 
4. Partage des Données 
Nous ne vendons pas vos données. Elles peuvent être partagées avec : 
 Prestataires tiers : Livreurs, services de paiement (ex : CIB, eDahabia), 
fournisseurs techniques. 
 Autorités légales : Si requis par la loi algérienne (ex : lutte contre la fraude). 
 Partenaires marketing : Uniquement avec votre accord explicite. 

 
5. Sécurité des Données 
Nous protégeons vos données via : 
 Chiffrement SSL pour les transactions en ligne. 
 Stockage sécurisé sur des serveurs hébergés en Algérie ou chez des partenaires 
certifiés. 
 Restrictions d’accès aux données sensibles. 
 
6. Vos Droits (Loi Algérienne 18-07) 
You can : 
 Accéder à vos données ou demander a copy. 
 Correct inaccurate information. 
 Delete your account and data (right to be forgotten). 
 Object to the use of your data for marketing. 
 Withdraw your consent at any time. 
For any question, contact us via : <EMAIL>. 
 
7. Cookies 
We use cookies for : 
 Facilitate navigation. 
 Personalize advertisements. 
 Analyze audience. 
You can disable cookies via your browser settings. 
 
8. Modifications de la Politique 
Any update will be notified by email or via a notification on the platform. The 
updated version will be available on this page. 
 
9. Contact Us 
For any question : 
 E-mail : <EMAIL> 
 Adresse : Village Taourirt Amrane Ain El Hammam, Tizi Ouzou 
 Téléphone : 06-67-05-65-23 
 
© Sharyoo 2025 – All rights reserved. 
  `;

  const termsOfUseContent = `
Conditions Générales d’Utilisation de Sharyoo 
Dernière mise à jour : 25 MAI 2025 
Applicables à tout utilisateur de la plateforme Sharyoo (site web et application 
mobile). 
 
1. Acceptation des Conditions 
En utilisant Sharyoo, vous acceptez sans réserve les présentes Conditions 
Générales d’Utilisation (CGU). Si vous n’êtes pas d’accord, veuillez ne pas 
accéder à la plateforme. 
 
2. Compte Utilisateur 
 Création : Vous devez fournir des informations exactes et complètes pour créer 
un compte. 
 Confidentialité : Vous êtes responsable de la sécurité de votre compte (mot de 
passe, accès, etc.). 
 Suppression : Vous pouvez fermer votre compte via les paramètres ou en nous 
contactant. 
 
3. Commandes et Paiements 
 Passation : Une commande est définitive une fois le paiement validé. 
 Prix : Les prix sont en DZD (ou autre devise précisée) et TTC, sauf indication 
contraire. 
 Paiement sécurisé : Transactions cryptées via des partenaires agréés (CIB, 
eDahabia, etc.). 
 Facturation : Une facture électronique est envoyée après validation de la 
commande. 
 
4. Livraisons et Retours 
 Zone de livraison : Algérie ( G specific regions specified on the platform). 
 Délais : Indiqués lors de la commande (susceptibles de varier en cas de force 
majeure). 
 Retours : Politique de retour disponible dans les Conditions Générales de 
Vente (CGV). 
 
5. Propriété Intellectuelle 
 Contenu Sharyoo : Tous les éléments (logos, textes, images) sont protégés par 
le droit algérien et international. 
 Utilisation interdite : Toute reproduction ou exploitation commerciale sans 
autorisation écrite est illégale. 

 
6. Obligations des Utilisateurs 
Il est interdit de : 
 Utiliser la plateforme à des fins illégales ou frauduleuses. 
 Publier des contenus discriminatoires, violents ou contraires à l’ordre public 
algérien. 
 Perturber le fonctionnement technique de Sharyoo (ex : hacking, spam). 
 
7. Limitation de Responsabilité 
 Disponibilité : Sharyoo ne garantit pas un accès continu ou exempt d’erreurs. 
 Produits : Les descriptions et photos des produits sont fournies par les 
vendeurs. Sharyoo décline toute responsabilité en cas d’inexactitude. 
 Liens externes : Nous ne contrôlons pas les sites tiers liés à notre plateforme. 
 
8. Résiliation 
Sharyoo se réserve le droit de suspendre ou résilier votre compte en cas de : 
 Violation des CGU. 
 Comportement frauduleux ou nuisible à la communauté. 
 
9. Litiges et Droit Applicable 
 Loi applicable : Droit algérien. 
 Tribunaux compétents : Tribunaux algériens du siège social de Sharyoo. 
 Médiation : En cas de litige, nous privilégions une résolution à l’amiable avant 
toute action judiciaire. 
 
10. Modifications des CGU 
Les CGU peuvent être mises à jour. Les utilisateurs seront notifiés par e-mail ou 
via une alerte sur la plateforme. 
 
11. Contact 
For any question : 
 E-mail :  <EMAIL> 
Adresse : Village Taourirt Amrane, Ain El Hammam, Tizi Ouzou 
 Téléphone : 06-67-05-65-23 
 
© Sharyoo 2025 – All rights reserved. 
  `;

  return (
      <Router>
        {isAuthenticated ? (
          <Dashboard onLogout={handleLogout}>
            <Routes>
              <Route path="/dashboard" element={<DashboardContent />} />

              {/* Orders Routes - Using new page wrapper */}
              <Route path="/orders" element={<OrdersPage />} />
              <Route path="/orders/recent" element={<OrdersPage />} />
              <Route path="/orders/activities" element={<OrdersPage />} />
              <Route path="/orders/popular-products" element={<OrdersPage />} />

              {/* Products Routes - Using new page wrapper */}
              <Route path="/products" element={<ProductsPage />} />
              <Route path="/products/all" element={<ProductsPage />} />
              <Route path="/products/add" element={<ProductsPage />} />
              <Route path="/products/categories" element={<ProductsPage />} />

              {/* Boutique Route */}
              <Route path="/boutique" element={<BoutiquePage />} />

              {/* Clients Routes - Using new page wrapper */}
              <Route path="/clients" element={<ClientsPage />} />
              <Route path="/clients/list" element={<ClientsPage />} />
              <Route path="/clients/groups" element={<ClientsPage />} />

              {/* Analytics Routes - Using new page wrapper */}
              <Route path="/analytics" element={<AnalyticsPage />} />
              <Route path="/analytics/sales" element={<AnalyticsPage />} />
              <Route path="/analytics/performance" element={<AnalyticsPage />} />

              {/* Settings Routes - Using new page wrapper */}
              <Route path="/settings" element={<SettingsPage />} />
              <Route path="/settings/general" element={<SettingsPage />} />
              <Route path="/settings/shipping" element={<SettingsPage />} />
              <Route path="/settings/payment" element={<SettingsPage />} />



              <Route path="*" element={<Navigate to="/dashboard" replace />} />
            </Routes>
          </Dashboard>
        ) : (
          <div className="min-h-screen bg-white font-sans custom-scrollbar-hide">
            <Navbar openLoginModal={openLoginModal} openRegisterModal={openRegisterModal} />
            <main>
              <Hero openRegisterModal={openRegisterModal} onLoginSuccess={handleLogin} />
              <Features />
              <HowItWorks openRegisterModal={openRegisterModal} />
              <Pricing openRegisterModal={openRegisterModal} />
              <Testimonials />
              <CallToAction openRegisterModal={openRegisterModal} />
            </main>
            <Footer 
              openPrivacyModal={openPrivacyModal} 
              openTermsModal={openTermsModal} 
              openFaqModal={openFaqModal}
              openContactModal={openContactModal}
              openDocumentationModal={openDocumentationModal}
              openAboutUsModal={openAboutUsModal}
              openCareersModal={openCareersModal}
              openBlogModal={openBlogModal}
              openEasyStoreCreationModal={openEasyStoreCreationModal}
              openArabicSupportModal={openArabicSupportModal}
              openDataSecurityModal={openDataSecurityModal}
            />
          </div>
        )}
        
        {showLoginModal && <AuthModal isOpen={showLoginModal} onClose={closeAuthModal} type="login" openTermsModal={() => openTermsModal(true)} />}
        {showRegisterModal && <AuthModal isOpen={showRegisterModal} onClose={closeAuthModal} type="register" openTermsModal={() => openTermsModal(true)} />}
        {showPrivacyModal && <ContentModal isOpen={showPrivacyModal} onClose={closePrivacyModal} content={privacyPolicyContent} title={t('privacy')} />}
        {showTermsModal && <ContentModal isOpen={showTermsModal} onClose={closeTermsModal} content={termsOfUseContent} title={t('terms')} onGoBack={() => setShowRegisterModal(true)} />}
        {showFaqModal && <FaqModal isOpen={showFaqModal} onClose={closeFaqModal} />}
        {showContactModal && <ContactModal isOpen={showContactModal} onClose={closeContactModal} />}
        {showDocumentationModal && <DocumentationModal isOpen={showDocumentationModal} onClose={closeDocumentationModal} />}
        {showAboutUsModal && <ContentModal isOpen={showAboutUsModal} onClose={closeAboutUsModal} content={t('about_us_content')} title={t('about_us_title')} />}
        {showCareersModal && <ContentModal isOpen={showCareersModal} onClose={closeCareersModal} content={t('careers_content')} title={t('careers_title')} />}
        {showBlogModal && <ContentModal isOpen={showBlogModal} onClose={closeBlogModal} content={t('blog_content')} title={t('blog_title')} />}
        {showEasyStoreCreationModal && <ContentModal isOpen={showEasyStoreCreationModal} onClose={closeEasyStoreCreationModal} content={t('feature_easy_store_creation_modal_content')} title={t('feature_easy_store_creation_title')} />}
        {showArabicSupportModal && <ContentModal isOpen={showArabicSupportModal} onClose={closeArabicSupportModal} content={t('feature_arabic_support_modal_content')} title={t('feature_arabic_support_title')} />}
        {showDataSecurityModal && <ContentModal isOpen={showDataSecurityModal} onClose={closeDataSecurityModal} content={t('feature_data_security_modal_content')} title={t('feature_data_security_title')} />}
        <div className="fixed bottom-4 right-4 space-y-2">
          {notifications.map((notification) => (
            <NotificationToast
              key={notification.id}
              message={notification.message}
              type={notification.type}
              onClose={() => removeNotification(notification.id)}
            />
          ))}
        </div>
      </Router>
  );
};

// Animated counter component
interface AnimatedCounterProps {
  value: string;
  duration?: number;
}

const AnimatedCounter: React.FC<AnimatedCounterProps> = ({ value, duration = 1000 }) => {
  const [displayValue, setDisplayValue] = useState('0');
  const [isAnimating, setIsAnimating] = useState(false);
  const previousValueRef = useRef(value);

  useEffect(() => {
    // Only animate if the value has changed
    if (previousValueRef.current !== value) {
      setIsAnimating(true);

      // Extract numeric part and suffix (like DA, %)
      const numericMatch = value.match(/^([\d,]+(?:\.\d+)?)/);
      const suffix = value.replace(/^[\d,]+(?:\.\d+)?/, '');

      if (numericMatch) {
        const targetNumber = parseFloat(numericMatch[1].replace(/,/g, ''));
        const previousNumericMatch = previousValueRef.current.match(/^([\d,]+(?:\.\d+)?)/);
        const startNumber = previousNumericMatch ? parseFloat(previousNumericMatch[1].replace(/,/g, '')) : 0;

        const startTime = Date.now();

        const animate = () => {
          const elapsed = Date.now() - startTime;
          const progress = Math.min(elapsed / duration, 1);

          // Easing function for smooth animation
          const easeOutQuart = 1 - Math.pow(1 - progress, 4);

          const currentNumber = startNumber + (targetNumber - startNumber) * easeOutQuart;

          // Format the number with commas if it was originally formatted that way
          let formattedNumber;
          if (value.includes(',')) {
            formattedNumber = Math.round(currentNumber).toLocaleString();
          } else if (value.includes('.')) {
            formattedNumber = currentNumber.toFixed(1);
          } else {
            formattedNumber = Math.round(currentNumber).toString();
          }

          setDisplayValue(formattedNumber + suffix);

          if (progress < 1) {
            requestAnimationFrame(animate);
          } else {
            setDisplayValue(value);
            setIsAnimating(false);
          }
        };

        requestAnimationFrame(animate);
      } else {
        // If no numeric value found, just display the value directly
        setDisplayValue(value);
        setIsAnimating(false);
      }

      previousValueRef.current = value;
    } else {
      setDisplayValue(value);
    }
  }, [value, duration]);

  return (
    <span className={`transition-all duration-300 ${isAnimating ? 'text-blue-600' : ''}`}>
      {displayValue}
    </span>
  );
};

// A simple component to render the default dashboard content
const DashboardContent: React.FC = () => {
  const { t } = useLanguage();
  const { user } = useAuth();
  const navigate = useNavigate();

  // Date filter state
  const [selectedDateRange, setSelectedDateRange] = useState('7d');
  const [showDateDropdown, setShowDateDropdown] = useState(false);

  // Navigation functions for different sections
  const navigateToSection = (section: string) => {
    navigate(section);
  };

  // Date range options
  const dateRangeOptions = [
    { value: '24h', label: 'Dernières 24h', comparison: 'vs 24h précédentes' },
    { value: '7d', label: '7 derniers jours', comparison: 'vs 7 jours précédents' },
    { value: '30d', label: '30 derniers jours', comparison: 'vs 30 jours précédents' },
    { value: '90d', label: '90 derniers jours', comparison: 'vs 90 jours précédents' },
    { value: 'ytd', label: 'Année en cours', comparison: 'vs année précédente' },
    { value: 'custom', label: 'Période personnalisée', comparison: 'vs période précédente' }
  ];

  // Get current date range label
  const getCurrentDateRangeLabel = () => {
    const option = dateRangeOptions.find(opt => opt.value === selectedDateRange);
    return option ? option.label : 'Période sélectionnée';
  };

  // Get comparison text
  const getComparisonText = () => {
    const option = dateRangeOptions.find(opt => opt.value === selectedDateRange);
    return option ? option.comparison : 'vs période précédente';
  };

  // Get current metrics from dashboard data (real data from backend)
  const currentMetrics = dashboardData?.metrics || {
    totalRevenue: { value: '0', change: '0%', positive: true },
    averageOrderValue: { value: '0', change: '0%', positive: true },
    totalOrders: { value: '0', change: '0%', positive: true },
    conversionRate: { value: '0%', change: '0%', positive: true },
    newCustomers: { value: '0', change: '0%', positive: true },
    uniqueVisitors: { value: '0', change: '0%', positive: true },
    grossMargin: { value: '0%', change: '0%', positive: true },
    customerLoyalty: { value: '0%', change: '0%', positive: true }
  };

  // Get recent orders from dashboard data (real data from backend)
  const recentOrders = dashboardData?.recentOrders || [];

  // Get top products from dashboard data (real data from backend)
  const topProducts = dashboardData?.topProducts || [];

  // Check if user is new (has no data)
  const isNewUser = !dashboardData || (
    dashboardData.stats.totalProducts === 0 &&
    dashboardData.stats.totalClients === 0 &&
    dashboardData.recentOrders.length === 0
  );

  // Handle loading state
  if (loadingDashboard) {
    return (
      <main className="flex-1 overflow-y-auto bg-gray-50 p-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Chargement du tableau de bord...</span>
        </div>
      </main>
    );
  }

  // Show welcome message for new users
  if (isNewUser) {
    return (
      <main className="flex-1 overflow-y-auto bg-gray-50 p-6">
        <WelcomeNewTenant
          tenantName={user?.first_name ? `${user.first_name}'s Store` : 'Votre Boutique'}
          onGetStarted={() => navigate('/products/add')}
        />
        <EmptyTenantStats />
        <EmptyState
          type="dashboard"
          onAction={() => navigate('/products/add')}
        />
      </main>
    );
  }

  return (
    <main className="flex-1 overflow-y-auto bg-gray-50 p-6">
      {/* Date Filter Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{t('dashboard')}</h1>
            <p className="text-sm text-gray-500 mt-1">{t('dashboard_subtitle')}</p>
          </div>
          <div className="relative">
            <button
              onClick={() => setShowDateDropdown(!showDateDropdown)}
              className="flex items-center space-x-2 px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">{getCurrentDateRangeLabel()}</span>
              <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform ${showDateDropdown ? 'rotate-180' : ''}`} />
            </button>

            {showDateDropdown && (
              <div className="absolute right-0 mt-2 w-56 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                <div className="py-1">
                  {dateRangeOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => {
                        setSelectedDateRange(option.value);
                        setShowDateDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 ${
                        selectedDateRange === option.value
                          ? 'bg-blue-50 text-blue-700 font-medium'
                          : 'text-gray-700'
                      }`}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Financial Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div
          onClick={() => navigateToSection('/analytics/sales')}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer hover:shadow-md hover:border-blue-300 transition-all duration-200 group"
        >
          <div className="text-center">
            <div className="flex justify-center mb-3">
              <div className="bg-blue-50 p-3 rounded-lg group-hover:bg-blue-100 transition-colors duration-200">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <p className="text-gray-500 text-sm font-medium mb-2">{t('total_revenue')}</p>
            <h3 className="text-2xl font-bold text-gray-900 mb-3">
              <AnimatedCounter value={currentMetrics.totalRevenue.value} duration={1200} /> DA
            </h3>
            <div className="flex items-center justify-center">
              {currentMetrics.totalRevenue.positive ? (
                <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm font-medium ${currentMetrics.totalRevenue.positive ? 'text-green-600' : 'text-red-600'}`}>
                {currentMetrics.totalRevenue.change}
              </span>
              <span className="text-xs text-gray-500 ml-2">{getComparisonText()}</span>
            </div>
          </div>
        </div>

        <div
          onClick={() => navigateToSection('/orders')}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer hover:shadow-md hover:border-green-300 transition-all duration-200 group"
        >
          <div className="text-center">
            <div className="flex justify-center mb-3">
              <div className="bg-green-50 p-3 rounded-lg group-hover:bg-green-100 transition-colors duration-200">
                <ShoppingBag className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <p className="text-gray-500 text-sm font-medium mb-2">{t('average_order_value')}</p>
            <h3 className="text-2xl font-bold text-gray-900 mb-3">
              <AnimatedCounter value={currentMetrics.averageOrderValue.value} duration={1200} /> DA
            </h3>
            <div className="flex items-center justify-center">
              {currentMetrics.averageOrderValue.positive ? (
                <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm font-medium ${currentMetrics.averageOrderValue.positive ? 'text-green-600' : 'text-red-600'}`}>
                {currentMetrics.averageOrderValue.change}
              </span>
              <span className="text-xs text-gray-500 ml-2">{getComparisonText()}</span>
            </div>
          </div>
        </div>

        <div
          onClick={() => navigateToSection('/orders')}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer hover:shadow-md hover:border-purple-300 transition-all duration-200 group"
        >
          <div className="text-center">
            <div className="flex justify-center mb-3">
              <div className="bg-purple-50 p-3 rounded-lg group-hover:bg-purple-100 transition-colors duration-200">
                <Package className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <p className="text-gray-500 text-sm font-medium mb-2">{t('total_orders')}</p>
            <h3 className="text-2xl font-bold text-gray-900 mb-3">
              <AnimatedCounter value={currentMetrics.totalOrders.value} duration={1200} />
            </h3>
            <div className="flex items-center justify-center">
              {currentMetrics.totalOrders.positive ? (
                <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm font-medium ${currentMetrics.totalOrders.positive ? 'text-green-600' : 'text-red-600'}`}>
                {currentMetrics.totalOrders.change}
              </span>
              <span className="text-xs text-gray-500 ml-2">{getComparisonText()}</span>
            </div>
          </div>
        </div>

        <div
          onClick={() => navigateToSection('/analytics')}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer hover:shadow-md hover:border-indigo-300 transition-all duration-200 group"
        >
          <div className="text-center">
            <div className="flex justify-center mb-3">
              <div className="bg-indigo-50 p-3 rounded-lg group-hover:bg-indigo-100 transition-colors duration-200">
                <TrendingUp className="h-6 w-6 text-indigo-600" />
              </div>
            </div>
            <p className="text-gray-500 text-sm font-medium mb-2">{t('conversion_rate')}</p>
            <h3 className="text-2xl font-bold text-gray-900 mb-3">
              <AnimatedCounter value={currentMetrics.conversionRate.value} duration={1200} />
            </h3>
            <div className="flex items-center justify-center">
              {currentMetrics.conversionRate.positive ? (
                <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm font-medium ${currentMetrics.conversionRate.positive ? 'text-green-600' : 'text-red-600'}`}>
                {currentMetrics.conversionRate.change}
              </span>
              <span className="text-xs text-gray-500 ml-2">{getComparisonText()}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Secondary Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div
          onClick={() => navigateToSection('/clients')}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer hover:shadow-md hover:border-yellow-300 transition-all duration-200 group"
        >
          <div className="text-center">
            <div className="flex justify-center mb-3">
              <div className="bg-yellow-50 p-3 rounded-lg group-hover:bg-yellow-100 transition-colors duration-200">
                <Users className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
            <p className="text-gray-500 text-sm font-medium mb-2">{t('new_customers')}</p>
            <h3 className="text-2xl font-bold text-gray-900 mb-3">
              <AnimatedCounter value={currentMetrics.newCustomers.value} duration={1200} />
            </h3>
            <div className="flex items-center justify-center">
              {currentMetrics.newCustomers.positive ? (
                <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm font-medium ${currentMetrics.newCustomers.positive ? 'text-green-600' : 'text-red-600'}`}>
                {currentMetrics.newCustomers.change}
              </span>
              <span className="text-xs text-gray-500 ml-2">{getComparisonText()}</span>
            </div>
          </div>
        </div>

        <div
          onClick={() => navigateToSection('/analytics')}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer hover:shadow-md hover:border-orange-300 transition-all duration-200 group"
        >
          <div className="text-center">
            <div className="flex justify-center mb-3">
              <div className="bg-orange-50 p-3 rounded-lg group-hover:bg-orange-100 transition-colors duration-200">
                <Eye className="h-6 w-6 text-orange-600" />
              </div>
            </div>
            <p className="text-gray-500 text-sm font-medium mb-2">{t('unique_visitors')}</p>
            <h3 className="text-2xl font-bold text-gray-900 mb-3">
              <AnimatedCounter value={currentMetrics.uniqueVisitors.value} duration={1200} />
            </h3>
            <div className="flex items-center justify-center">
              {currentMetrics.uniqueVisitors.positive ? (
                <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm font-medium ${currentMetrics.uniqueVisitors.positive ? 'text-green-600' : 'text-red-600'}`}>
                {currentMetrics.uniqueVisitors.change}
              </span>
              <span className="text-xs text-gray-500 ml-2">{getComparisonText()}</span>
            </div>
          </div>
        </div>

        <div
          onClick={() => navigateToSection('/analytics/sales')}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer hover:shadow-md hover:border-emerald-300 transition-all duration-200 group"
        >
          <div className="text-center">
            <div className="flex justify-center mb-3">
              <div className="bg-emerald-50 p-3 rounded-lg group-hover:bg-emerald-100 transition-colors duration-200">
                <BarChart2 className="h-6 w-6 text-emerald-600" />
              </div>
            </div>
            <p className="text-gray-500 text-sm font-medium mb-2">{t('gross_margin')}</p>
            <h3 className="text-2xl font-bold text-gray-900 mb-3">
              <AnimatedCounter value={currentMetrics.grossMargin.value} duration={1200} />
            </h3>
            <div className="flex items-center justify-center">
              {currentMetrics.grossMargin.positive ? (
                <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm font-medium ${currentMetrics.grossMargin.positive ? 'text-green-600' : 'text-red-600'}`}>
                {currentMetrics.grossMargin.change}
              </span>
              <span className="text-xs text-gray-500 ml-2">{getComparisonText()}</span>
            </div>
          </div>
        </div>

        <div
          onClick={() => navigateToSection('/clients')}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer hover:shadow-md hover:border-pink-300 transition-all duration-200 group"
        >
          <div className="text-center">
            <div className="flex justify-center mb-3">
              <div className="bg-pink-50 p-3 rounded-lg group-hover:bg-pink-100 transition-colors duration-200">
                <Star className="h-6 w-6 text-pink-600" />
              </div>
            </div>
            <p className="text-gray-500 text-sm font-medium mb-2">{t('loyal_customers')}</p>
            <h3 className="text-2xl font-bold text-gray-900 mb-3">
              <AnimatedCounter value={currentMetrics.customerLoyalty.value} duration={1200} />
            </h3>
            <div className="flex items-center justify-center">
              {currentMetrics.customerLoyalty.positive ? (
                <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm font-medium ${currentMetrics.customerLoyalty.positive ? 'text-green-600' : 'text-red-600'}`}>
                {currentMetrics.customerLoyalty.change}
              </span>
              <span className="text-xs text-gray-500 ml-2">{getComparisonText()}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Regional Performance & Payment Methods */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Regional Performance */}
        <div
          onClick={() => navigateToSection('/analytics/regions')}
          className="bg-white rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:shadow-md hover:border-blue-300 transition-all duration-200"
        >
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Performance par wilaya</h3>
            <p className="text-sm text-gray-500 mt-1">Répartition des ventes par région</p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-900">Alger</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">1,250,000 DA</div>
                  <div className="text-xs text-gray-500">342 commandes (35.2%)</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-900">Oran</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">720,000 DA</div>
                  <div className="text-xs text-gray-500">198 commandes (20.8%)</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-900">Constantine</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">580,000 DA</div>
                  <div className="text-xs text-gray-500">156 commandes (16.7%)</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-900">Annaba</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">320,000 DA</div>
                  <div className="text-xs text-gray-500">89 commandes (9.2%)</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-900">Autres wilayas</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">630,000 DA</div>
                  <div className="text-xs text-gray-500">167 commandes (18.1%)</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Methods */}
        <div
          onClick={() => navigateToSection('/analytics/payments')}
          className="bg-white rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:shadow-md hover:border-green-300 transition-all duration-200"
        >
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Méthodes de paiement</h3>
            <p className="text-sm text-gray-500 mt-1">Répartition des paiements</p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-900">Paiement à la livraison</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">65.2%</div>
                  <div className="text-xs text-gray-500">1,598,000 DA</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-900">CCP</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">18.5%</div>
                  <div className="text-xs text-gray-500">453,250 DA</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-900">Edahabia</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">12.8%</div>
                  <div className="text-xs text-gray-500">313,600 DA</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-900">Carte bancaire</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">3.5%</div>
                  <div className="text-xs text-gray-500">85,750 DA</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent orders */}
      <div
        onClick={() => navigateToSection('/orders')}
        className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 cursor-pointer hover:shadow-md hover:border-purple-300 transition-all duration-200"
      >
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">{t('recent_orders')}</h3>
            <span className="text-sm text-blue-600 hover:text-blue-800 font-medium">{t('view_all_orders')}</span>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Commande</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Wilaya</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Paiement</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">#ORD-2024-001</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Ahmed Benali</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Alger</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">4,250 DA</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">CCP</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                    Livré
                  </span>
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">#ORD-2024-002</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Fatima Zahra</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Oran</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">1,850 DA</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Paiement à la livraison</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                    En cours
                  </span>
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">#ORD-2024-003</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Karim Hadj</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Constantine</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">3,600 DA</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Edahabia</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                    Expédié
                  </span>
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">#ORD-2024-004</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Amina Kadi</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Annaba</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">2,100 DA</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Paiement à la livraison</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                    Confirmé
                  </span>
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">#ORD-2024-005</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Mohamed Brahim</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Sétif</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">5,750 DA</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">CCP</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                    Livré
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Top Products & Inventory Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        {/* Top Products */}
        <div
          onClick={() => navigateToSection('/products')}
          className="lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:shadow-md hover:border-green-300 transition-all duration-200"
        >
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Produits les plus vendus</h3>
              <span className="text-sm text-blue-600 hover:text-blue-800 font-medium">Voir tout</span>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Produit</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Catégorie</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vendus</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenu</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Samsung Galaxy A54</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                      Électronique
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">156</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">14,040,000 DA</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">AirPods Pro 2</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Accessoires
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">234</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">5,850,000 DA</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">HP Pavilion 15</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                      Informatique
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">89</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">12,450,000 DA</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Apple Watch Series 9</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Accessoires
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">67</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">8,040,000 DA</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">iPad Air M2</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                      Électronique
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">45</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">6,750,000 DA</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Inventory Alerts */}
        <div
          onClick={() => navigateToSection('/products/inventory')}
          className="bg-white rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:shadow-md hover:border-red-300 transition-all duration-200"
        >
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Alertes stock</h3>
            <p className="text-sm text-gray-500 mt-1">Produits en rupture ou stock faible</p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                <div>
                  <p className="text-sm font-medium text-red-900">iPhone 15 Pro</p>
                  <p className="text-xs text-red-600">Stock critique</p>
                </div>
                <div className="text-right">
                  <span className="text-lg font-bold text-red-600">5</span>
                  <p className="text-xs text-red-500">unités</p>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                <div>
                  <p className="text-sm font-medium text-yellow-900">Samsung Galaxy S24</p>
                  <p className="text-xs text-yellow-600">Stock faible</p>
                </div>
                <div className="text-right">
                  <span className="text-lg font-bold text-yellow-600">12</span>
                  <p className="text-xs text-yellow-500">unités</p>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                <div>
                  <p className="text-sm font-medium text-yellow-900">MacBook Air M2</p>
                  <p className="text-xs text-yellow-600">Stock faible</p>
                </div>
                <div className="text-right">
                  <span className="text-lg font-bold text-yellow-600">8</span>
                  <p className="text-xs text-yellow-500">unités</p>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                <div>
                  <p className="text-sm font-medium text-red-900">AirPods Pro 2</p>
                  <p className="text-xs text-red-600">Stock critique</p>
                </div>
                <div className="text-right">
                  <span className="text-lg font-bold text-red-600">3</span>
                  <p className="text-xs text-red-500">unités</p>
                </div>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t border-gray-200">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  navigateToSection('/products/inventory');
                }}
                className="w-full text-sm text-blue-600 hover:text-blue-800 font-medium"
              >
                Gérer l'inventaire
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Activity timeline */}
      <div className="bg-white rounded-lg shadow p-5">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-800">Activité récente</h3>
          <a href="#" className="text-sm text-blue-600 hover:text-blue-800">Voir tout</a>
        </div>
        <div className="space-y-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-10 w-10 rounded-full bg-blue-100 text-blue-600">
                <ShoppingBag className="h-5 w-5" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-800">Nouvelle commande #1234</p>
              <p className="text-sm text-gray-500">TAHAR NAIT ALI a passé une commande de 4,250.00 DA</p>
              <p className="text-xs text-gray-400 mt-1 flex items-center">
                <Clock className="h-3 w-3 mr-1" /> Il y a 35 minutes
              </p>
            </div>
          </div>
          <div className="flex">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-10 w-10 rounded-full bg-green-100 text-green-600">
                <Users className="h-5 w-5" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-800">Nouveau client</p>
              <p className="text-sm text-gray-500">Fatima Zahra s'est inscrite</p>
              <p className="text-xs text-gray-400 mt-1 flex items-center">
                <Clock className="h-3 w-3 mr-1" /> Il y a 2 heures
              </p>
            </div>
          </div>
          <div className="flex">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-10 w-10 rounded-full bg-purple-100 text-purple-600">
                <Package className="h-5 w-5" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-800">Commande expédiée #1236</p>
              <p className="text-sm text-gray-500">La commande de Karim Hadj a été expédiée</p>
              <p className="text-xs text-gray-400 mt-1 flex items-center">
                <Clock className="h-3 w-3 mr-1" /> Il y a 3 heures
              </p>
            </div>
          </div>
          <div className="flex">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-10 w-10 rounded-full bg-yellow-100 text-yellow-600">
                <AlertCircle className="h-5 w-5" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-800">Commande en attente #1243</p>
              <p className="text-sm text-gray-500">La commande de Nadia Ferhat est en attente de paiement</p>
              <p className="text-xs text-gray-400 mt-1 flex items-center">
                <Clock className="h-3 w-3 mr-1" /> Il y a 5 heures
              </p>
            </div>
          </div>
          <div className="flex">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-10 w-10 rounded-full bg-red-100 text-red-600">
                <XCircle className="h-5 w-5" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-800">Commande annulée #1241</p>
              <p className="text-sm text-gray-500">Leila Mansouri a annulé sa commande</p>
              <p className="text-xs text-gray-400 mt-1 flex items-center">
                <Clock className="h-3 w-3 mr-1" /> Il y a 6 heures
              </p>
            </div>
          </div>
          <div className="flex">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-10 w-10 rounded-full bg-indigo-100 text-indigo-600">
                <Star className="h-5 w-5" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-800">Nouvel avis client</p>
              <p className="text-sm text-gray-500">Mohamed Benali a laissé un avis 5 étoiles</p>
              <p className="text-xs text-gray-400 mt-1 flex items-center">
                <Clock className="h-3 w-3 mr-1" /> Il y a 8 heures
              </p>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default AppContent;
