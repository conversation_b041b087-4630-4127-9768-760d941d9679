import React from 'react';
import { LanguageProvider } from './context/LanguageContext';
import { AuthProvider } from './context/AuthContext';
import AppContent from './components/AppContent';
import AuthErrorHandler from './components/AuthErrorHandler';

function App() {
  return (
    <AuthErrorHandler>
      <AuthProvider>
        <LanguageProvider>
          <AppContent />
        </LanguageProvider>
      </AuthProvider>
    </AuthErrorHandler>
  );
}

export default App;
