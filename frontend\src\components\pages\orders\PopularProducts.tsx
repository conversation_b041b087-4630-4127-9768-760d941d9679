import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, Minus, Star, Package } from 'lucide-react';

interface Product {
  id: string;
  name: string;
  category: string;
  image: string;
  sold: number;
  revenue: string;
  price: string;
  stock: number;
  rating: number;
  reviews: number;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
}

const PopularProducts: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call to fetch products
    const fetchProducts = async () => {
      try {
        // In a real app, this would be an API call
        // For now, we'll simulate an empty response for new users
        const response = await new Promise<Product[]>((resolve) => {
          setTimeout(() => {
            // Return empty array for new users
            resolve([]);
          }, 1000);
        });

        setProducts(response);
      } catch (error) {
        console.error('Error fetching products:', error);
        setProducts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Sample products for demonstration (this would be removed in production)
  const sampleProducts = [
    {
      id: 'PRD-001',
      name: 'Smartphone Samsung Galaxy A54',
      category: 'Électronique',
      image: '/images/products/samsung-galaxy-a54.jpg',
      sold: 156,
      revenue: '1,248,000.00 DA',
      price: '8,000.00 DA',
      stock: 45,
      rating: 4.8,
      reviews: 89,
      trend: 'up',
      trendPercentage: 15.2
    },
    {
      id: 'PRD-002',
      name: 'Écouteurs sans fil AirPods Pro',
      category: 'Audio',
      image: '/images/products/airpods-pro.jpg',
      sold: 134,
      revenue: '536,000.00 DA',
      price: '4,000.00 DA',
      stock: 78,
      rating: 4.6,
      reviews: 156,
      trend: 'up',
      trendPercentage: 8.7
    },
    {
      id: 'PRD-003',
      name: 'Montre connectée Apple Watch Series 9',
      category: 'Wearables',
      image: '/images/products/apple-watch-s9.jpg',
      sold: 98,
      revenue: '441,000.00 DA',
      price: '4,500.00 DA',
      stock: 23,
      rating: 4.9,
      reviews: 67,
      trend: 'up',
      trendPercentage: 22.1
    },
    {
      id: 'PRD-004',
      name: 'Ordinateur portable HP Pavilion 15',
      category: 'Informatique',
      image: '/images/products/hp-pavilion-15.jpg',
      sold: 67,
      revenue: '938,000.00 DA',
      price: '14,000.00 DA',
      stock: 12,
      rating: 4.4,
      reviews: 43,
      trend: 'down',
      trendPercentage: -5.3
    },
    {
      id: 'PRD-005',
      name: 'Tablette iPad Air 10.9"',
      category: 'Tablettes',
      image: '/images/products/ipad-air.jpg',
      sold: 54,
      revenue: '648,000.00 DA',
      price: '12,000.00 DA',
      stock: 31,
      rating: 4.7,
      reviews: 38,
      trend: 'stable',
      trendPercentage: 0.8
    },
    {
      id: 'PRD-006',
      name: 'Casque Gaming SteelSeries Arctis 7',
      category: 'Gaming',
      image: '/images/products/steelseries-arctis-7.jpg',
      sold: 43,
      revenue: '215,000.00 DA',
      price: '5,000.00 DA',
      stock: 67,
      rating: 4.5,
      reviews: 29,
      trend: 'up',
      trendPercentage: 12.4
    },
    {
      id: 'PRD-007',
      name: 'Clavier mécanique Logitech MX Keys',
      category: 'Accessoires',
      image: '/images/products/logitech-mx-keys.jpg',
      sold: 38,
      revenue: '152,000.00 DA',
      price: '4,000.00 DA',
      stock: 89,
      rating: 4.3,
      reviews: 24,
      trend: 'stable',
      trendPercentage: -1.2
    },
    {
      id: 'PRD-008',
      name: 'Souris Gaming Razer DeathAdder V3',
      category: 'Gaming',
      image: '/images/products/razer-deathadder-v3.jpg',
      sold: 32,
      revenue: '96,000.00 DA',
      price: '3,000.00 DA',
      stock: 156,
      rating: 4.6,
      reviews: 18,
      trend: 'up',
      trendPercentage: 18.9
    }
  ];

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down': return <TrendingDown className="w-4 h-4 text-red-500" />;
      case 'stable': return <Minus className="w-4 h-4 text-gray-500" />;
      default: return <Minus className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      case 'stable': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const getStockStatus = (stock: number) => {
    if (stock <= 20) return { color: 'text-red-600 bg-red-50', label: 'Stock faible' };
    if (stock <= 50) return { color: 'text-yellow-600 bg-yellow-50', label: 'Stock moyen' };
    return { color: 'text-green-600 bg-green-50', label: 'En stock' };
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-3 h-3 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <div className="p-6 bg-white rounded-xl shadow-sm border border-gray-200">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-bold text-gray-900">Produits populaires</h2>
          <p className="text-sm text-gray-600 mt-1">Produits les plus vendus cette période</p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
            Top {products.length} produits
          </div>
          <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
            Voir tout
          </button>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Chargement des produits...</span>
        </div>
      ) : products.length === 0 ? (
        <div className="text-center py-12">
          <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun produit</h3>
          <p className="text-gray-500 mb-6">Vous n'avez pas encore ajouté de produits à votre boutique. Commencez par créer votre premier produit.</p>
          <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
            Ajouter un produit
          </button>
        </div>
      ) : (
        <>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Produit</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Catégorie</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Vendus</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Prix unitaire</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Revenu total</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Stock</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Note</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Tendance</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {products.slice(0, 5).map((product, index) => {
              const stockStatus = getStockStatus(product.stock);
              return (
                <tr key={index} className="hover:bg-gray-50 transition-colors duration-150">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-12 w-12">
                        <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-sm">
                          <Package className="w-6 h-6 text-white" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-semibold text-gray-900">{product.name}</div>
                        <div className="text-xs text-gray-500">ID: {product.id}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                      {product.category}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-semibold text-gray-900">{product.sold}</div>
                      <div className="text-xs text-gray-500">unités vendues</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-semibold text-gray-900">{product.price}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-semibold text-emerald-600">{product.revenue}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${stockStatus.color}`}>
                      <div className={`w-2 h-2 rounded-full mr-2 ${
                        product.stock <= 20 ? 'bg-red-500' :
                        product.stock <= 50 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}></div>
                      {product.stock} en stock
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex items-center">
                        {renderStars(product.rating)}
                      </div>
                      <span className="ml-2 text-sm font-medium text-gray-900">{product.rating}</span>
                      <span className="ml-1 text-xs text-gray-500">({product.reviews})</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getTrendIcon(product.trend)}
                      <span className={`ml-2 text-sm font-semibold ${getTrendColor(product.trend)}`}>
                        {product.trendPercentage > 0 ? '+' : ''}{product.trendPercentage}%
                      </span>
                    </div>
                  </td>
                </tr>
              );
              })}
            </tbody>
          </table>
        </div>

        {products.length > 5 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <button className="w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium py-2 hover:bg-blue-50 rounded-lg transition-colors duration-150">
              Voir tous les produits ({products.length})
            </button>
          </div>
        )}
        </>
      )}
    </div>
  );
};

export default PopularProducts;
