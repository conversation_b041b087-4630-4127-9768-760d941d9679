/**
 * Authentication Context for Sharyoo Frontend
 * Provides authentication state and methods throughout the app
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { authService, User, RegisterData, LoginData, AuthResponse } from '../services/authService';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginData) => Promise<AuthResponse>;
  register: (userData: RegisterData) => Promise<AuthResponse>;
  logout: () => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<AuthResponse>;
  forgotPassword: (email: string) => Promise<AuthResponse>;
  resetPassword: (accessToken: string, refreshToken: string, newPassword: string) => Promise<AuthResponse>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Initialize authentication state
    const initAuth = async () => {
      try {
        const currentUser = await authService.getCurrentUser();
        setUser(currentUser);
      } catch (error: any) {
        console.error('Auth initialization error:', error);

        // If it's a refresh token error, clear the session
        if (error?.message?.includes('Invalid Refresh Token') ||
            error?.message?.includes('Refresh Token Not Found')) {
          console.log('Clearing invalid session');
          await authService.logout();
          setUser(null);
        }
      } finally {
        setLoading(false);
      }
    };

    initAuth();

    // Listen to auth state changes
    const unsubscribe = authService.onAuthStateChange((user) => {
      setUser(user);
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const login = async (credentials: LoginData): Promise<AuthResponse> => {
    setLoading(true);
    try {
      const result = await authService.login(credentials);
      if (result.success && result.data?.user) {
        setUser(result.data.user);
      }
      return result;
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData: RegisterData): Promise<AuthResponse> => {
    setLoading(true);
    try {
      const result = await authService.register(userData);
      if (result.success && result.data?.user) {
        setUser(result.data.user);
      }
      return result;
    } finally {
      setLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    setLoading(true);
    try {
      await authService.logout();
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<User>): Promise<AuthResponse> => {
    const result = await authService.updateProfile(updates);
    if (result.success && result.data?.user) {
      setUser(result.data.user);
    }
    return result;
  };

  const forgotPassword = async (email: string): Promise<AuthResponse> => {
    return authService.forgotPassword(email);
  };

  const resetPassword = async (
    accessToken: string,
    refreshToken: string,
    newPassword: string
  ): Promise<AuthResponse> => {
    return authService.resetPassword(accessToken, refreshToken, newPassword);
  };

  const refreshUser = async (): Promise<void> => {
    try {
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('Refresh user error:', error);
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    updateProfile,
    forgotPassword,
    resetPassword,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
